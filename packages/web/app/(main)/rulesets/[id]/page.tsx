'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import {
  Button,
  Card,
  Badge,
  Text,
  Heading,
  Box,
  Flex,
  Container,
  Separator,
  Dialog,
  DropdownMenu,
} from '@radix-ui/themes';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Share2,
  Copy,
  Eye,
  Lock,
  Calendar,
  User,
  Package,
  MoreVertical,
  ExternalLink,
  Download,
} from 'lucide-react';
import { RuleCard } from '@/components/rule-card';
import { RulesetForm } from '@/components/ruleset-form';
import { useSession } from '@/lib/auth-client';
import { toast } from 'sonner';
import {
  useRuleset,
  useUpdateRuleset,
  useDeleteRuleset,
  Ruleset,
  UpdateRulesetData,
} from '@/hooks/use-ruleset-queries';
import { formatDistanceToNow } from 'date-fns';

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

export default function RulesetPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { data: session } = useSession();

  const rulesetId = params.id as string;
  const shareToken = searchParams.get('token');

  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);

  // Fetch ruleset data
  const {
    data: ruleset,
    isLoading,
    error
  } = useRuleset(rulesetId, shareToken || undefined);

  // Mutation hooks
  const updateRulesetMutation = useUpdateRuleset();
  const deleteRulesetMutation = useDeleteRuleset();

  // Check if user is the owner
  const isOwner = session?.user?.id === ruleset?.user.id;
  const isPublic = ruleset?.visibility === 'PUBLIC';

  useEffect(() => {
    if (error) {
      toast.error('Failed to load ruleset');
      router.push('/rulesets');
    }
  }, [error, router]);

  const handleEdit = () => {
    setShowEditDialog(true);
  };

  const handleDelete = async () => {
    if (!ruleset) return;

    if (window.confirm(`Are you sure you want to delete "${ruleset.name}"? This action cannot be undone.`)) {
      try {
        await deleteRulesetMutation.mutateAsync(ruleset.id);
        toast.success('Ruleset deleted successfully');
        router.push('/rulesets');
      } catch (error) {
        console.error('Error deleting ruleset:', error);
      }
    }
  };

  const handleShare = () => {
    setShowShareDialog(true);
  };

  const handleCopyShareLink = async () => {
    if (!ruleset?.shareToken) return;

    const shareUrl = `${window.location.origin}/rulesets/${ruleset.id}?token=${ruleset.shareToken}`;

    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard');
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link');
    }
  };

  const handleCopyRulesetId = async () => {
    if (!ruleset) return;

    try {
      await navigator.clipboard.writeText(ruleset.id);
      toast.success('Ruleset ID copied to clipboard');
    } catch (error) {
      console.error('Failed to copy ID:', error);
      toast.error('Failed to copy ID');
    }
  };

  const handleUpdateRuleset = async (data: UpdateRulesetData) => {
    try {
      await updateRulesetMutation.mutateAsync(data);
      setShowEditDialog(false);
    } catch (error) {
      console.error('Error updating ruleset:', error);
    }
  };

  const handleExportRuleset = () => {
    if (!ruleset) return;

    const exportData = {
      name: ruleset.name,
      description: ruleset.description,
      rules: ruleset.rules.map(({ rule }) => ({
        title: rule.title,
        description: rule.description,
        content: rule.content,
        ideType: rule.ideType,
        tags: rule.tags.map(({ tag }) => tag.name),
      })),
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${ruleset.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_ruleset.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Ruleset exported successfully');
  };

  if (isLoading) {
    return (
      <Container size="4" py="8">
        <Box className="text-center">
          <Heading size="6" mb="4">Loading Ruleset...</Heading>
          <div className="animate-pulse space-y-4">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
            <div className="grid md:grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-24"></div>
              ))}
            </div>
          </div>
        </Box>
      </Container>
    );
  }

  if (!ruleset) {
    return (
      <Container size="4" py="8">
        <Box className="text-center">
          <Heading size="6" mb="4">Ruleset Not Found</Heading>
          <Text color="gray" mb="4">
            The ruleset you're looking for doesn't exist or you don't have permission to view it.
          </Text>
          <Button onClick={() => router.push('/rulesets')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Rulesets
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container size="4" py="8">
      <Flex direction="column" gap="6">
        {/* Header */}
        <Flex justify="between" align="start" gap="4">
          <Button variant="ghost" onClick={() => router.push('/rulesets')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Rulesets
          </Button>

          {isOwner && (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button variant="ghost" size="2">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Item onClick={handleEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Ruleset
                </DropdownMenu.Item>
                <DropdownMenu.Item onClick={handleExportRuleset}>
                  <Download className="mr-2 h-4 w-4" />
                  Export Ruleset
                </DropdownMenu.Item>
                {isPublic && (
                  <DropdownMenu.Item onClick={handleShare}>
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Ruleset
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Separator />
                <DropdownMenu.Item onClick={handleDelete} color="red">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Ruleset
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          )}
        </Flex>

        {/* Ruleset Info */}
        <Card>
          <Box p="6">
            <Flex justify="between" align="start" mb="4">
              <Box className="flex-1">
                <Heading size="7" mb="2">{ruleset.name}</Heading>
                {ruleset.description && (
                  <Text size="3" color="gray" mb="4">
                    {ruleset.description}
                  </Text>
                )}
              </Box>
              <Badge
                variant="soft"
                color={isPublic ? 'green' : 'gray'}
                size="2"
              >
                {isPublic ? (
                  <>
                    <Eye className="w-3 h-3 mr-1" />
                    Public
                  </>
                ) : (
                  <>
                    <Lock className="w-3 h-3 mr-1" />
                    Private
                  </>
                )}
              </Badge>
            </Flex>

            <Flex gap="6" wrap="wrap">
              <Flex align="center" gap="2">
                <User className="w-4 h-4 text-gray-500" />
                <Text size="2" color="gray">
                  Created by {ruleset.user.name || ruleset.user.email}
                </Text>
              </Flex>
              <Flex align="center" gap="2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <Text size="2" color="gray">
                  Updated {formatDistanceToNow(new Date(ruleset.updatedAt), { addSuffix: true })}
                </Text>
              </Flex>
              <Flex align="center" gap="2">
                <Package className="w-4 h-4 text-gray-500" />
                <Text size="2" color="gray">
                  {ruleset.rules.length} rule{ruleset.rules.length !== 1 ? 's' : ''}
                </Text>
              </Flex>
            </Flex>

            {/* Quick Actions */}
            <Flex gap="3" mt="4">
              <Button variant="soft" onClick={handleCopyRulesetId}>
                <Copy className="mr-2 h-4 w-4" />
                Copy ID
              </Button>
              {isPublic && ruleset.shareToken && (
                <Button variant="soft" onClick={handleCopyShareLink}>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Copy Share Link
                </Button>
              )}
              <Button variant="soft" onClick={handleExportRuleset}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </Flex>
          </Box>
        </Card>

        <Separator size="4" />

        {/* Rules Section */}
        <Box>
          <Heading size="5" mb="4">
            Rules in this Ruleset ({ruleset.rules.length})
          </Heading>

          {ruleset.rules.length === 0 ? (
            <Card className="py-12 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <Heading size="4" mb="2">No rules in this ruleset</Heading>
              <Text color="gray" mb="4">
                This ruleset doesn't contain any rules yet.
              </Text>
              {isOwner && (
                <Button onClick={handleEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Add Rules
                </Button>
              )}
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {ruleset.rules
                .sort((a, b) => a.order - b.order)
                .map(({ rule }) => (
                  <RuleCard
                    key={rule.id}
                    rule={rule}
                    isOwner={false} // Don't show edit/delete actions for individual rules in ruleset view
                  />
                ))}
            </div>
          )}
        </Box>

        {/* Edit Dialog */}
        <Dialog.Root open={showEditDialog} onOpenChange={setShowEditDialog}>
          <Dialog.Content className="max-w-5xl max-h-[90vh] w-[95vw] overflow-hidden">
            <Dialog.Title className="sr-only">Edit Ruleset</Dialog.Title>
            <div className="overflow-y-auto max-h-[85vh]">
              <RulesetForm
                initialData={ruleset}
                onSubmit={handleUpdateRuleset}
                onCancel={() => setShowEditDialog(false)}
                isLoading={updateRulesetMutation.isPending}
              />
            </div>
          </Dialog.Content>
        </Dialog.Root>

        {/* Share Dialog */}
        <Dialog.Root open={showShareDialog} onOpenChange={setShowShareDialog}>
          <Dialog.Content className="max-w-md">
            <Dialog.Title>Share Ruleset</Dialog.Title>
            <Box className="space-y-4">
              <Text size="2" color="gray">
                Share this public ruleset with others using the link below:
              </Text>

              {ruleset.shareToken && (
                <Box className="space-y-3">
                  <Box className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">
                    <Text size="1" className="font-mono break-all">
                      {`${window.location.origin}/rulesets/${ruleset.id}?token=${ruleset.shareToken}`}
                    </Text>
                  </Box>

                  <Flex gap="2">
                    <Button onClick={handleCopyShareLink} className="flex-1">
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Link
                    </Button>
                    <Button variant="soft" onClick={() => setShowShareDialog(false)}>
                      Close
                    </Button>
                  </Flex>
                </Box>
              )}
            </Box>
          </Dialog.Content>
        </Dialog.Root>
      </Flex>
    </Container>
  );
}