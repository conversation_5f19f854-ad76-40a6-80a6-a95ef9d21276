'use client';

import { useState, useMemo } from 'react';
import { Metada<PERSON> } from "next";
import {
  Button,
  TextField,
  Select,
  Badge,
  Card,
  Tabs,
  Dialog,
  Text,
  Heading,
  Box,
  Flex,
  Container,
} from '@radix-ui/themes';
import { Plus, Search, Filter, Package, Users, Eye, Lock, Loader2 } from 'lucide-react';
import { RulesetCard } from '@/components/ruleset-card';
import { RulesetForm } from '@/components/ruleset-form';
import { useSession } from '@/lib/auth-client';
import { toast } from 'sonner';
import {
  useRulesets,
  useCreateRuleset,
  useUpdateRuleset,
  useDeleteRuleset,
  Ruleset,
  CreateRulesetData,
  UpdateRulesetData,
} from '@/hooks/use-ruleset-queries';

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

export default function RulesetsPage() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState('');
  const [visibilityFilter, setVisibilityFilter] = useState<'ALL' | 'PUBLIC' | 'PRIVATE'>('ALL');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingRuleset, setEditingRuleset] = useState<Ruleset | null>(null);

  // Memoize the filters object to prevent unnecessary re-renders
  const rulesetFilters = useMemo(() => ({
    search: searchQuery || undefined,
    visibility: visibilityFilter !== 'ALL' ? visibilityFilter : undefined,
  }), [searchQuery, visibilityFilter]);

  // Use query hooks for data fetching
  const {
    data: rulesets = [],
    isLoading: rulesetsLoading,
    error: rulesetsError
  } = useRulesets(rulesetFilters);

  // Mutation hooks
  const createRulesetMutation = useCreateRuleset();
  const updateRulesetMutation = useUpdateRuleset();
  const deleteRulesetMutation = useDeleteRuleset();

  // Filter rulesets by ownership
  const userRulesets = rulesets.filter(ruleset => ruleset.user.id === session?.user?.id);
  const publicRulesets = rulesets.filter(ruleset => ruleset.visibility === 'PUBLIC');

  const stats = {
    totalRulesets: userRulesets.length,
    publicRulesets: userRulesets.filter(r => r.visibility === 'PUBLIC').length,
    privateRulesets: userRulesets.filter(r => r.visibility === 'PRIVATE').length,
    totalRules: userRulesets.reduce((acc, ruleset) => acc + ruleset.rules.length, 0),
  };

  const handleCreateRuleset = () => {
    setEditingRuleset(null);
    setShowCreateDialog(true);
  };

  const handleEditRuleset = (ruleset: Ruleset) => {
    setEditingRuleset(ruleset);
    setShowCreateDialog(true);
  };

  const handleSubmitRuleset = async (data: CreateRulesetData | UpdateRulesetData) => {
    try {
      if ('id' in data) {
        await updateRulesetMutation.mutateAsync(data);
      } else {
        await createRulesetMutation.mutateAsync(data);
      }
      setShowCreateDialog(false);
      setEditingRuleset(null);
    } catch (error) {
      console.error('Error saving ruleset:', error);
    }
  };

  const handleDeleteRuleset = async (ruleset: Ruleset) => {
    if (window.confirm(`Are you sure you want to delete "${ruleset.name}"? This action cannot be undone.`)) {
      try {
        await deleteRulesetMutation.mutateAsync(ruleset.id);
      } catch (error) {
        console.error('Error deleting ruleset:', error);
      }
    }
  };

  const handleCancelForm = () => {
    setShowCreateDialog(false);
    setEditingRuleset(null);
  };

  // Temporarily bypass authentication for testing
  const isTestMode = process.env.NODE_ENV === 'development';

  if (!session?.user && !isTestMode) {
    return (
      <Container size="4" py="8">
        <Box className="text-center">
          <Heading size="6" mb="4">Please sign in to continue</Heading>
          <Button asChild>
            <a href="/auth/signin">Sign In</a>
          </Button>
        </Box>
      </Container>
    );
  }

  // Use mock session in test mode
  const effectiveSession = session || (isTestMode ? {
    user: {
      id: "test-user-id",
      name: "Test User",
      email: "<EMAIL>",
      image: null
    }
  } : null);

  // Show loading state for initial load
  if (rulesetsLoading && rulesets.length === 0) {
    return (
      <Container size="4" py="8">
        <Box className="text-center">
          <Heading size="6" mb="4">Ruleset Management</Heading>
          <Text color="gray" mb="8">
            Loading your rulesets...
          </Text>
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-20"></div>
              ))}
            </div>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
              ))}
            </div>
          </div>
        </Box>
      </Container>
    );
  }

  return (
    <Container size="4" py="8">
      <Flex direction="column" gap="8">
        {/* Header */}
        <Flex
          justify="between"
          align="center"
          direction={{ initial: 'column', md: 'row' }}
          gap="4"
        >
          <Box className="text-center md:text-left">
            <Heading size="8" weight="bold" mb="2">Ruleset Management</Heading>
            <Text size="4" color="gray">
              Create and manage collections of AI rules for quick deployment
            </Text>
          </Box>
          <Button onClick={handleCreateRuleset} size="3">
            <Plus className="mr-2 h-4 w-4" />
            New Ruleset
          </Button>
        </Flex>

        {/* Stats */}
        <Box className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <Flex justify="between" align="center" pb="2">
              <Text size="2" weight="medium" color="gray">Total Rulesets</Text>
              <Package className="h-4 w-4 text-gray-500" />
            </Flex>
            <Text size="7" weight="bold">{stats.totalRulesets}</Text>
          </Card>

          <Card>
            <Flex justify="between" align="center" pb="2">
              <Text size="2" weight="medium" color="gray">Public Rulesets</Text>
              <Users className="h-4 w-4 text-gray-500" />
            </Flex>
            <Text size="7" weight="bold">{stats.publicRulesets}</Text>
          </Card>

          <Card>
            <Flex justify="between" align="center" pb="2">
              <Text size="2" weight="medium" color="gray">Private Rulesets</Text>
              <Lock className="h-4 w-4 text-gray-500" />
            </Flex>
            <Text size="7" weight="bold">{stats.privateRulesets}</Text>
          </Card>

          <Card>
            <Flex justify="between" align="center" pb="2">
              <Text size="2" weight="medium" color="gray">Total Rules</Text>
              <Eye className="h-4 w-4 text-gray-500" />
            </Flex>
            <Text size="7" weight="bold">{stats.totalRules}</Text>
          </Card>
        </Box>

        {/* Filters */}
        <Flex gap="4" direction={{ initial: 'column', md: 'row' }}>
          <Box className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <TextField.Root
              placeholder="Search rulesets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </Box>

          <Select.Root value={visibilityFilter} onValueChange={(value: 'ALL' | 'PUBLIC' | 'PRIVATE') => setVisibilityFilter(value)}>
            <Select.Trigger className="w-full md:w-48">
              {visibilityFilter === 'ALL' ? 'All Visibility' : visibilityFilter}
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="ALL">All Visibility</Select.Item>
              <Select.Item value="PUBLIC">
                <Flex align="center" gap="2">
                  <Eye className="w-4 h-4" />
                  Public
                </Flex>
              </Select.Item>
              <Select.Item value="PRIVATE">
                <Flex align="center" gap="2">
                  <Lock className="w-4 h-4" />
                  Private
                </Flex>
              </Select.Item>
            </Select.Content>
          </Select.Root>
        </Flex>

        {/* Content */}
        <Tabs.Root defaultValue="my-rulesets" className="space-y-6">
          <Tabs.List>
            <Tabs.Trigger value="my-rulesets">My Rulesets ({userRulesets.length})</Tabs.Trigger>
            <Tabs.Trigger value="community">Community ({publicRulesets.length})</Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="my-rulesets" className="space-y-6">
            {userRulesets.length === 0 ? (
              <Card className="py-12 text-center">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <Heading size="4" mb="2">No rulesets yet</Heading>
                <Text color="gray" mb="4">
                  Create your first ruleset to organize your AI prompt rules
                </Text>
                <Button onClick={handleCreateRuleset}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Ruleset
                </Button>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {userRulesets.map((ruleset) => (
                  <RulesetCard
                    key={ruleset.id}
                    ruleset={ruleset}
                    onEdit={handleEditRuleset}
                    onDelete={handleDeleteRuleset}
                    showActions={true}
                  />
                ))}
              </div>
            )}
          </Tabs.Content>

          <Tabs.Content value="community" className="space-y-6">
            {publicRulesets.length === 0 ? (
              <Card className="py-12 text-center">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <Heading size="4" mb="2">No community rulesets found</Heading>
                <Text color="gray">
                  Try adjusting your filters or check back later
                </Text>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {publicRulesets.map((ruleset) => (
                  <RulesetCard
                    key={ruleset.id}
                    ruleset={ruleset}
                    showActions={ruleset.user.id === effectiveSession?.user?.id}
                    onEdit={ruleset.user.id === effectiveSession?.user?.id ? handleEditRuleset : undefined}
                    onDelete={ruleset.user.id === effectiveSession?.user?.id ? handleDeleteRuleset : undefined}
                  />
                ))}
              </div>
            )}
          </Tabs.Content>
        </Tabs.Root>

        {/* Create/Edit Dialog */}
        <Dialog.Root open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <Dialog.Content className="max-w-5xl max-h-[90vh] w-[95vw] overflow-hidden">
            <Dialog.Title className="sr-only">
              {editingRuleset ? 'Edit Ruleset' : 'Create New Ruleset'}
            </Dialog.Title>
            <div className="overflow-y-auto max-h-[85vh]">
              <RulesetForm
                initialData={editingRuleset || undefined}
                onSubmit={handleSubmitRuleset}
                onCancel={handleCancelForm}
                isLoading={createRulesetMutation.isPending || updateRulesetMutation.isPending}
              />
            </div>
          </Dialog.Content>
        </Dialog.Root>
      </Flex>
    </Container>
  );
}