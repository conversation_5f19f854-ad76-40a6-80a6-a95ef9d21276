import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { generateMDXContent } from '@/lib/mdx-utils';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Skip database operations during build
  const isBuildTime = process.env.NODE_ENV === 'production' && !process.env.VERCEL && !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    return NextResponse.json(
      { error: 'Service temporarily unavailable' },
      { status: 503 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const ruleId = searchParams.get('id');

    if (!ruleId) {
      return NextResponse.json(
        { error: 'Rule ID is required' },
        { status: 400 }
      );
    }

    const rule = await prisma.rule.findFirst({
      where: {
        id: ruleId,
        visibility: 'PUBLIC',
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!rule) {
      return NextResponse.json(
        { error: 'Rule not found or not public' },
        { status: 404 }
      );
    }

    // Generate MDX content
    const mdxContent = generateMDXContent(rule, { includeId: true, useISODates: true });

    // Return as plain text with MDX content type
    return new NextResponse(mdxContent, {
      headers: {
        'Content-Type': 'text/mdx; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error fetching raw rule:', error);
    return NextResponse.json(
      { error: 'Failed to fetch rule' },
      { status: 500 }
    );
  }
}

