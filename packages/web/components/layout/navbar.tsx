"use client";

import Link from "next/link";
import { useState } from "react";
import { useTheme } from "next-themes";
import { <PERSON>, Sun, Code, User, LogOut, Settings, Menu, X, Github as GitHubIcon } from "lucide-react";
import {
  Box,
  Flex,
  Container,
  Button,
  DropdownMenu,
  Avatar,
  Text,
  Separator,
  IconButton
} from "@radix-ui/themes";
import { useSession, signOut } from "@/lib/auth-client";
import { LanguageSwitcher } from "@/components/ui/language-switcher";
import { IDEPreferenceManager } from "@/components/ide-preferences/ide-preference-manager";
import { type Locale } from "@/lib/i18n";
// import { useLingui } from '@lingui/react';

interface NavbarProps {
  locale: Locale;
}

export function Navbar({ locale }: NavbarProps) {
  const { theme, setTheme } = useTheme();
  const { data: session } = useSession();
  const [showIDEPreferences, setShowIDEPreferences] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  // const { i18n } = useLingui();

  const handleSignOut = async () => {
    await signOut();
    setDropdownOpen(false);
    setMobileMenuOpen(false);
  };

  const handleIDEPreferences = () => {
    setShowIDEPreferences(true);
    setDropdownOpen(false);
    setMobileMenuOpen(false);
  };

  const handleNavigation = () => {
    setDropdownOpen(false);
    setMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const navigationLinks = [
    { href: "/dashboard", label: "Dashboard" },
    { href: "/rulesets", label: "Rulesets" },
    { href: "/templates", label: "Templates" },
    { href: "/ides", label: "IDEs" },
    { href: "/tutorials", label: "Tutorials" },
    { href: "/docs", label: "Docs" },
    { href: "/shared", label: "Community" },
  ];

  return (
    <>
      <Box
        asChild
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 50,
          width: '100%',
          borderBottom: '1px solid var(--gray-6)',
          backgroundColor: 'var(--color-panel-translucent)',
          backdropFilter: 'blur(8px)',
        }}
      >
        <nav>
          <Container size="4" className="mobile-container">
            <Flex
              align="center"
              justify="between"
              height={{ initial: "56px", xs: "64px" }}
              gap="4"
            >
              {/* Logo and Desktop Navigation */}
              <Flex align="center" gap={{ initial: "4", xs: "8" }}>
                <Link href="/" onClick={handleNavigation}>
                  <Flex align="center" gap="2" asChild>
                    <Box>
                      <Code size={20} className="xs:w-6 xs:h-6 text-[var(--accent-9)]" />
                      <Text
                        size={{ initial: "4", xs: "5" }}
                        weight="bold"
                        color="gray"
                        highContrast
                      >
                        OnlyRules
                      </Text>
                    </Box>
                  </Flex>
                </Link>

                {/* Desktop Navigation */}
                <Box display={{ initial: "none", md: "block" }}>
                  <Flex align="center" gap="6">
                    {navigationLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        onClick={handleNavigation}
                      >
                        <Text
                          size="2"
                          weight="medium"
                          className="transition-colors hover:text-[var(--accent-9)]"
                        >
                          {link.label}
                        </Text>
                      </Link>
                    ))}
                  </Flex>
                </Box>
              </Flex>

              {/* Desktop Controls */}
              <Flex align="center" gap={{ initial: "2", xs: "4" }}>
                <Box display={{ initial: "none", xs: "block" }}>
                  <Flex align="center" gap={{ initial: "2", xs: "3" }}>
                    <LanguageSwitcher currentLocale={locale} />

                    <Button variant="ghost" size="2" asChild className="touch-target">
                      <Link
                        href="https://github.com/ranglang/onlyrules"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Flex align="center" gap="2">
                          <GitHubIcon size={16} />
                          <Box display={{ initial: "none", sm: "block" }}>
                            <Text size="2">GitHub</Text>
                          </Box>
                        </Flex>
                      </Link>
                    </Button>

                    <IconButton
                      variant="ghost"
                      size="2"
                      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                      className="touch-target"
                      aria-label="Toggle theme"
                    >
                      <Box style={{ position: 'relative' }}>
                        <Sun
                          size={16}
                          className="rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
                        />
                        <Moon
                          size={16}
                          className="absolute top-0 left-0 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                        />
                      </Box>
                    </IconButton>

                    {session?.user ? (
                      <DropdownMenu.Root open={dropdownOpen} onOpenChange={setDropdownOpen}>
                        <DropdownMenu.Trigger>
                          <IconButton
                            variant="ghost"
                            size="2"
                            className="touch-target"
                            style={{ borderRadius: '50%' }}
                          >
                            <Avatar
                              size="2"
                              src={session.user.image || ""}
                              fallback={session.user.name?.charAt(0) || session.user.email?.charAt(0) || "U"}
                            />
                          </IconButton>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content size="2" align="end">
                          <Box p="2">
                            <Flex direction="column" gap="1">
                              {session.user.name && (
                                <Text size="2" weight="medium">{session.user.name}</Text>
                              )}
                              {session.user.email && (
                                <Text
                                  size="1"
                                  color="gray"
                                  style={{ maxWidth: '200px' }}
                                  className="truncate"
                                >
                                  {session.user.email}
                                </Text>
                              )}
                            </Flex>
                          </Box>
                          <Separator size="4" />
                          <DropdownMenu.Item asChild>
                            <Link href="/dashboard" onClick={handleNavigation}>
                              <Flex align="center" gap="2">
                                <User size={16} />
                                <Text size="2">Dashboard</Text>
                              </Flex>
                            </Link>
                          </DropdownMenu.Item>
                          <DropdownMenu.Item onClick={handleIDEPreferences}>
                            <Flex align="center" gap="2">
                              <Code size={16} />
                              <Text size="2">IDE Preferences</Text>
                            </Flex>
                          </DropdownMenu.Item>
                          <DropdownMenu.Item asChild>
                            <Link href="/settings" onClick={handleNavigation}>
                              <Flex align="center" gap="2">
                                <Settings size={16} />
                                <Text size="2">Settings</Text>
                              </Flex>
                            </Link>
                          </DropdownMenu.Item>
                          <Separator size="4" />
                          <DropdownMenu.Item onClick={handleSignOut}>
                            <Flex align="center" gap="2">
                              <LogOut size={16} />
                              <Text size="2">Sign Out</Text>
                            </Flex>
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    ) : (
                      <Button variant="ghost" size="2" asChild className="touch-target">
                        <Link href="/auth/signin" onClick={handleNavigation}>
                          <Text size="2">Sign In</Text>
                        </Link>
                      </Button>
                    )}
                  </Flex>
                </Box>

                {/* Mobile Menu Button */}
                <Box display={{ xs: "none", initial: "block" }}>
                  <IconButton
                    variant="ghost"
                    size="2"
                    onClick={toggleMobileMenu}
                    className="touch-target"
                    aria-label="Toggle mobile menu"
                  >
                    {mobileMenuOpen ? (
                      <X size={20} />
                    ) : (
                      <Menu size={20} />
                    )}
                  </IconButton>
                </Box>
              </Flex>
            </Flex>
          </Container>
        </nav>
      </Box>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <Box
          display={{ xs: "none", initial: "block" }}
          style={{
            position: 'fixed',
            inset: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 40,
          }}
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Mobile Menu */}
      <Box
        display={{ xs: "none", initial: "block" }}
        style={{
          position: 'fixed',
          top: '56px',
          left: 0,
          right: 0,
          backgroundColor: 'var(--color-panel-solid)',
          borderBottom: '1px solid var(--gray-6)',
          boxShadow: 'var(--shadow-4)',
          zIndex: 50,
          transform: mobileMenuOpen ? 'translateY(0)' : 'translateY(-100%)',
          transition: 'transform 300ms ease-in-out',
        }}
      >
        <Container size="4" className="mobile-container">
          <Box py="4">
            <Flex direction="column" gap="4">
              {/* Mobile Navigation Links */}
              <Flex direction="column" gap="3">
                {navigationLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    onClick={handleNavigation}
                  >
                    <Box py="2" className="touch-target">
                      <Text
                        size="2"
                        weight="medium"
                        className="transition-colors hover:text-[var(--accent-9)]"
                      >
                        {link.label}
                      </Text>
                    </Box>
                  </Link>
                ))}
              </Flex>

              <Separator size="4" />

              {/* Mobile Controls */}
              <Flex direction="column" gap="3">
                <Flex align="center" justify="between">
                  <Text size="2" weight="medium">Language</Text>
                  <LanguageSwitcher currentLocale={locale} />
                </Flex>

                <Flex align="center" justify="between">
                  <Text size="2" weight="medium">Theme</Text>
                  <Button
                    variant="ghost"
                    size="2"
                    onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                    className="touch-target"
                  >
                    <Flex align="center" gap="2">
                      <Box style={{ position: 'relative' }}>
                        <Sun
                          size={16}
                          className="rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
                        />
                        <Moon
                          size={16}
                          className="absolute top-0 left-0 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                        />
                      </Box>
                      <Text size="2">{theme === "dark" ? "Light" : "Dark"}</Text>
                    </Flex>
                  </Button>
                </Flex>

                <Link
                  href="https://github.com/ranglang/onlyrules"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={handleNavigation}
                >
                  <Box py="2" className="touch-target">
                    <Flex align="center" gap="3">
                      <GitHubIcon size={16} />
                      <Text
                        size="2"
                        weight="medium"
                        className="transition-colors hover:text-[var(--accent-9)]"
                      >
                        GitHub
                      </Text>
                    </Flex>
                  </Box>
                </Link>
              </Flex>

              {/* Mobile User Menu */}
              {session?.user ? (
                <>
                  <Separator size="4" />
                  <Flex direction="column" gap="3">
                    <Flex align="center" gap="3" py="2">
                      <Avatar
                        size="2"
                        src={session.user.image || ""}
                        fallback={session.user.name?.charAt(0) || session.user.email?.charAt(0) || "U"}
                      />
                      <Flex direction="column" gap="1" style={{ minWidth: 0, flex: 1 }}>
                        {session.user.name && (
                          <Text size="2" weight="medium" className="truncate">
                            {session.user.name}
                          </Text>
                        )}
                        {session.user.email && (
                          <Text size="1" color="gray" className="truncate">
                            {session.user.email}
                          </Text>
                        )}
                      </Flex>
                    </Flex>

                    <Link href="/dashboard" onClick={handleNavigation}>
                      <Box py="2" className="touch-target">
                        <Flex align="center" gap="3">
                          <User size={16} />
                          <Text
                            size="2"
                            weight="medium"
                            className="transition-colors hover:text-[var(--accent-9)]"
                          >
                            Dashboard
                          </Text>
                        </Flex>
                      </Box>
                    </Link>

                    <Box
                      asChild
                      py="2"
                      className="touch-target"
                      style={{ width: '100%', textAlign: 'left' }}
                    >
                      <button onClick={handleIDEPreferences}>
                        <Flex align="center" gap="3">
                          <Code size={16} />
                          <Text
                            size="2"
                            weight="medium"
                            className="transition-colors hover:text-[var(--accent-9)]"
                          >
                            IDE Preferences
                          </Text>
                        </Flex>
                      </button>
                    </Box>

                    <Link href="/settings" onClick={handleNavigation}>
                      <Box py="2" className="touch-target">
                        <Flex align="center" gap="3">
                          <Settings size={16} />
                          <Text
                            size="2"
                            weight="medium"
                            className="transition-colors hover:text-[var(--accent-9)]"
                          >
                            Settings
                          </Text>
                        </Flex>
                      </Box>
                    </Link>

                    <Box
                      asChild
                      py="2"
                      className="touch-target"
                      style={{ width: '100%', textAlign: 'left' }}
                    >
                      <button onClick={handleSignOut}>
                        <Flex align="center" gap="3">
                          <LogOut size={16} />
                          <Text
                            size="2"
                            weight="medium"
                            color="red"
                            className="transition-colors hover:opacity-80"
                          >
                            Sign Out
                          </Text>
                        </Flex>
                      </button>
                    </Box>
                  </Flex>
                </>
              ) : (
                <>
                  <Separator size="4" />
                  <Button size="3" asChild className="touch-target" style={{ width: '100%' }}>
                    <Link href="/auth/signin" onClick={handleNavigation}>
                      <Text size="2" weight="medium">Sign In</Text>
                    </Link>
                  </Button>
                </>
              )}
            </Flex>
          </Box>
        </Container>
      </Box>

      {/* IDE Preferences Dialog */}
      <IDEPreferenceManager
        open={showIDEPreferences}
        onOpenChange={setShowIDEPreferences}
      />
    </>
  );
}