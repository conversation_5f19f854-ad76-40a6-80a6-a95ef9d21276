"use client";

import { useState } from "react";
import {
  Button,
  TextField,
  Text,
  Card,
  DropdownMenu,
  Box,
  Flex,
  Grid,
  Select
} from "@radix-ui/themes";
import {
  MoreVertical,
  Trash2,
  Copy,
  GripVertical,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import { CodeEditor } from "@/components/ui/code-editor";
import { RuleSection, ApplyType, APPLY_TYPE_METADATA } from "@/lib/store";
import { validateSection, getSectionSummary } from "@/lib/rule-sections";

interface RuleSectionEditorProps {
  section: RuleSection;
  index: number;
  totalSections: number;
  isExpanded: boolean;
  onUpdate: (section: RuleSection) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onToggleExpanded: () => void;
}

export function RuleSectionEditor({
  section,
  index,
  totalSections,
  isExpanded,
  onUpdate,
  onDelete,
  onDuplicate,
  onMoveUp,
  onMoveDown,
  onToggleExpanded,
}: RuleSectionEditorProps) {
  const [errors, setErrors] = useState<string[]>([]);

  const handleFieldChange = (field: keyof RuleSection, value: string | ApplyType) => {
    const updatedSection = { ...section, [field]: value };
    onUpdate(updatedSection);

    // Validate on change
    const validationErrors = validateSection(updatedSection);
    setErrors(validationErrors);
  };

  const sectionSummary = getSectionSummary(section);
  const hasErrors = errors.length > 0;

  return (
    <Card className={hasErrors ? 'error-border' : ''} style={{ transition: 'all 0.2s ease' }}>
      <div style={{ paddingBottom: 'var(--space-3)' }}>
        <Flex justify="between" align="center">
          <Flex align="center" gap="3" style={{ flex: 1 }}>
            <Flex align="center" gap="1">
              <GripVertical size={16} style={{ color: 'var(--gray-9)', cursor: 'grab' }} />
              <Text size="2" weight="medium" style={{ color: 'var(--gray-9)' }}>
                {index + 1}
              </Text>
            </Flex>

            <Box style={{ flex: 1 }}>
              <div className="font-semibold" style={{ fontSize: 'var(--font-size-3)' }}>
                {section.title || `Section ${index + 1}`}
              </div>
              {!isExpanded && (
                <Text size="2" style={{ color: 'var(--gray-9)', marginTop: 'var(--space-1)' }} truncate>
                  {sectionSummary}
                </Text>
              )}
            </Box>
          </Flex>

          <Flex align="center" gap="2">
            {/* Move buttons */}
            <Flex align="center">
              <Button
                variant="ghost"
                size="1"
                onClick={onMoveUp}
                disabled={index === 0}
                style={{ width: '32px', height: '32px', padding: 0 }}
              >
                <ChevronUp size={16} />
              </Button>
              <Button
                variant="ghost"
                size="1"
                onClick={onMoveDown}
                disabled={index === totalSections - 1}
                style={{ width: '32px', height: '32px', padding: 0 }}
              >
                <ChevronDown size={16} />
              </Button>
            </Flex>

            {/* Actions dropdown */}
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button variant="ghost" size="1" style={{ width: '32px', height: '32px', padding: 0 }}>
                  <MoreVertical size={16} />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="end">
                <DropdownMenu.Item onClick={onDuplicate}>
                  <Copy size={16} style={{ marginRight: 'var(--space-2)' }} />
                  Duplicate
                </DropdownMenu.Item>
                <DropdownMenu.Item
                  onClick={onDelete}
                  disabled={totalSections === 1}
                  className="error-text"
                >
                  <Trash2 size={16} style={{ marginRight: 'var(--space-2)' }} />
                  Delete
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>

            {/* Expand/Collapse button */}
            <Button
              variant="ghost"
              size="1"
              onClick={onToggleExpanded}
              style={{ width: '32px', height: '32px', padding: 0 }}
            >
              {isExpanded ? (
                <ChevronUp size={16} />
              ) : (
                <ChevronDown size={16} />
              )}
            </Button>
          </Flex>
        </Flex>

        {hasErrors && (
          <Box style={{ marginTop: 'var(--space-2)' }}>
            {errors.map((error, i) => (
              <Text key={i} size="2" className="error-text" style={{ display: 'block' }}>
                • {error}
              </Text>
            ))}
          </Box>
        )}
      </div>

      {isExpanded && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-4)' }}>
          {/* Section Title */}
          <Box>
            <Text as="label" htmlFor={`section-title-${section.id}`} size="2" weight="medium">Section Title</Text>
            <TextField.Root
              id={`section-title-${section.id}`}
              value={section.title}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              placeholder="Enter section title..."
            />
          </Box>

          {/* Optional Metadata */}
          <Grid columns={{ initial: '1', md: '2', lg: '4' }} gap="4">
            <Box>
              <Text as="label" htmlFor={`section-name-${section.id}`} size="2" weight="medium">Name (Optional)</Text>
              <TextField.Root
                id={`section-name-${section.id}`}
                value={section.name || ''}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder="e.g., global, stylesheet"
              />
            </Box>

            <Box>
              <Text as="label" htmlFor={`section-globs-${section.id}`} size="2" weight="medium">File Patterns (Optional)</Text>
              <TextField.Root
                id={`section-globs-${section.id}`}
                value={section.globs || ''}
                onChange={(e) => handleFieldChange('globs', e.target.value)}
                placeholder="e.g., **.css, *.js"
              />
            </Box>

            <Box>
              <Text as="label" htmlFor={`section-description-${section.id}`} size="2" weight="medium">Description (Optional)</Text>
              <TextField.Root
                id={`section-description-${section.id}`}
                value={section.description || ''}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Brief description"
              />
            </Box>

            <Box>
              <Text as="label" htmlFor={`section-apply-type-${section.id}`} size="2" weight="medium">Apply Type (Optional)</Text>
              <Select.Root
                value={section.applyType || ''}
                onValueChange={(value) => handleFieldChange('applyType', value as ApplyType)}
              >
                <Select.Trigger placeholder="Select apply type" />
                <Select.Content>
                  <Select.Item value="">None</Select.Item>
                  {Object.entries(APPLY_TYPE_METADATA).map(([key, metadata]) => (
                    <Select.Item key={key} value={key}>
                      <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <span style={{ fontWeight: 'medium' }}>{metadata.name}</span>
                        <span style={{ fontSize: 'var(--font-size-1)', color: 'var(--gray-9)' }}>
                          {metadata.description}
                        </span>
                      </div>
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select.Root>
            </Box>
          </Grid>

          {/* Section Content */}
          <Box>
            <Text as="label" htmlFor={`section-content-${section.id}`} size="2" weight="medium">Content</Text>
            <CodeEditor
              value={section.content}
              onChange={(value) => handleFieldChange('content', value)}
              placeholder="Enter section content (markdown supported)...."
            />
          </Box>
        </div>
      )}
    </Card>
  );
}
