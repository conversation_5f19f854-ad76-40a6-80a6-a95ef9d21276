import {
  parseRuleContent,
  formatRuleContent,
  createEmptySection,
  duplicateSection,
  moveSectionInArray,
  validateSection,
  updateSectionApplyType,
  isValidApplyType
} from '../rule-sections';

describe('Rule Sections Utilities', () => {
  describe('parseRuleContent', () => {
    it('should parse single section without delimiters', () => {
      const content = `# Basic Rules

This is a simple rule without sections.`;
      
      const sections = parseRuleContent(content);
      
      expect(sections).toHaveLength(1);
      expect(sections[0].title).toBe('Basic Rules');
      expect(sections[0].content).toBe(content);
      expect(sections[0].description).toBeUndefined();
      expect(sections[0].name).toBeUndefined();
      expect(sections[0].globs).toBeUndefined();
      expect(sections[0].applyType).toBeUndefined();
    });

    it('should parse multiple sections with metadata', () => {
      const content = `---
description: This is helpful for CSS files
name: global
globs: **.css
applyType: auto
---

# Basic AI Rules

## General Instructions
- Respond in a clear, concise manner

---
description: This is helpful for CSS files
globs: **.css
name: stylesheet
applyType: manual
---

# Content Guidelines
- Do not generate harmful content`;

      const sections = parseRuleContent(content);
      
      expect(sections).toHaveLength(2);
      
      // First section
      expect(sections[0].title).toBe('Basic AI Rules');
      expect(sections[0].description).toBe('This is helpful for CSS files');
      expect(sections[0].name).toBe('global');
      expect(sections[0].globs).toBe('**.css');
      expect(sections[0].applyType).toBe('auto');

      // Second section
      expect(sections[1].title).toBe('Content Guidelines');
      expect(sections[1].description).toBe('This is helpful for CSS files');
      expect(sections[1].name).toBe('stylesheet');
      expect(sections[1].globs).toBe('**.css');
      expect(sections[1].applyType).toBe('manual');
    });

    it('should handle empty content', () => {
      const sections = parseRuleContent('');

      expect(sections).toHaveLength(1);
      expect(sections[0].title).toBe('Section 1');
      expect(sections[0].content).toBe('');
    });

    it('should parse applyType metadata correctly', () => {
      const content = `---
description: Test section
applyType: always
---

# Test Section
Content here`;

      const sections = parseRuleContent(content);

      expect(sections).toHaveLength(1);
      expect(sections[0].applyType).toBe('always');
    });

    it('should ignore invalid applyType values', () => {
      const content = `---
description: Test section
applyType: invalid_value
---

# Test Section
Content here`;

      const sections = parseRuleContent(content);

      expect(sections).toHaveLength(1);
      expect(sections[0].applyType).toBeUndefined();
    });
  });

  describe('formatRuleContent', () => {
    it('should format single section without metadata', () => {
      const sections = [{
        id: '1',
        title: 'Basic Rules',
        content: '# Basic Rules\n\nThis is content.'
      }];
      
      const formatted = formatRuleContent(sections);
      
      expect(formatted).toBe('# Basic Rules\n\nThis is content.');
    });

    it('should format multiple sections with metadata', () => {
      const sections = [
        {
          id: '1',
          title: 'Basic Rules',
          content: '# Basic Rules\n\nContent 1',
          description: 'First section',
          name: 'basic',
          globs: '**.js',
          applyType: 'auto' as const
        },
        {
          id: '2',
          title: 'Advanced Rules',
          content: '# Advanced Rules\n\nContent 2'
        }
      ];

      const formatted = formatRuleContent(sections);

      expect(formatted).toContain('---');
      expect(formatted).toContain('description: First section');
      expect(formatted).toContain('name: basic');
      expect(formatted).toContain('globs: **.js');
      expect(formatted).toContain('applyType: auto');
      expect(formatted).toContain('# Basic Rules');
      expect(formatted).toContain('# Advanced Rules');
    });

    it('should format section with only applyType metadata', () => {
      const sections = [
        {
          id: '1',
          title: 'Test Section',
          content: '# Test Section\n\nContent',
          applyType: 'manual' as const
        }
      ];

      const formatted = formatRuleContent(sections);

      expect(formatted).toContain('---');
      expect(formatted).toContain('applyType: manual');
      expect(formatted).toContain('# Test Section');
    });
  });

  describe('createEmptySection', () => {
    it('should create empty section with correct title', () => {
      const section = createEmptySection(2);
      
      expect(section.title).toBe('Section 3');
      expect(section.content).toBe('');
      expect(section.id).toBeDefined();
    });
  });

  describe('duplicateSection', () => {
    it('should duplicate section with new ID and modified title', () => {
      const original = {
        id: '1',
        title: 'Original Section',
        content: 'Content',
        description: 'Description'
      };
      
      const duplicate = duplicateSection(original);
      
      expect(duplicate.id).not.toBe(original.id);
      expect(duplicate.title).toBe('Original Section (Copy)');
      expect(duplicate.content).toBe(original.content);
      expect(duplicate.description).toBe(original.description);
    });
  });

  describe('moveSectionInArray', () => {
    it('should move section from one position to another', () => {
      const sections = [
        { id: '1', title: 'Section 1', content: 'Content 1' },
        { id: '2', title: 'Section 2', content: 'Content 2' },
        { id: '3', title: 'Section 3', content: 'Content 3' }
      ];
      
      const moved = moveSectionInArray(sections, 0, 2);
      
      expect(moved[0].title).toBe('Section 2');
      expect(moved[1].title).toBe('Section 3');
      expect(moved[2].title).toBe('Section 1');
    });
  });

  describe('validateSection', () => {
    it('should return no errors for valid section', () => {
      const section = {
        id: '1',
        title: 'Valid Section',
        content: 'Valid content'
      };
      
      const errors = validateSection(section);
      
      expect(errors).toHaveLength(0);
    });

    it('should return errors for invalid section', () => {
      const section = {
        id: '1',
        title: '',
        content: ''
      };
      
      const errors = validateSection(section);
      
      expect(errors).toContain('Section title is required');
      expect(errors).toContain('Section content is required');
    });
  });

  describe('updateSectionApplyType', () => {
    it('should update applyType for specific section', () => {
      const sections = [
        { id: '1', title: 'Section 1', content: 'Content 1' },
        { id: '2', title: 'Section 2', content: 'Content 2', applyType: 'manual' as const },
        { id: '3', title: 'Section 3', content: 'Content 3' }
      ];

      const updated = updateSectionApplyType(sections, '2', 'auto');

      expect(updated[0]).toEqual(sections[0]); // Unchanged
      expect(updated[1].applyType).toBe('auto'); // Updated
      expect(updated[2]).toEqual(sections[2]); // Unchanged
    });

    it('should remove applyType when set to undefined', () => {
      const sections = [
        { id: '1', title: 'Section 1', content: 'Content 1', applyType: 'manual' as const }
      ];

      const updated = updateSectionApplyType(sections, '1', undefined);

      expect(updated[0].applyType).toBeUndefined();
    });

    it('should not modify sections when ID not found', () => {
      const sections = [
        { id: '1', title: 'Section 1', content: 'Content 1' }
      ];

      const updated = updateSectionApplyType(sections, 'nonexistent', 'auto');

      expect(updated).toEqual(sections);
    });
  });

  describe('isValidApplyType', () => {
    it('should return true for valid apply types', () => {
      expect(isValidApplyType('auto')).toBe(true);
      expect(isValidApplyType('manual')).toBe(true);
      expect(isValidApplyType('always')).toBe(true);
    });

    it('should return false for invalid apply types', () => {
      expect(isValidApplyType('invalid')).toBe(false);
      expect(isValidApplyType('')).toBe(false);
      expect(isValidApplyType('AUTO')).toBe(false); // Case sensitive
      expect(isValidApplyType('never')).toBe(false);
    });
  });
});
